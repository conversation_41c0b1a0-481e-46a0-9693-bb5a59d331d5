{"name": "zyro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^6.23.1", "@clerk/themes": "^2.2.53", "@e2b/code-interpreter": "^1.5.1", "@hookform/resolvers": "^5.1.1", "@inngest/agent-kit": "^0.8.3", "@prisma/client": "^6.10.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.2", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3", "@types/prismjs": "^1.26.5", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "inngest": "^3.39.2", "lucide": "^0.525.0", "lucide-react": "^0.524.0", "next": "15.3.4", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "random-word-slugs": "^0.1.7", "rate-limiter-flexible": "^7.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "react-textarea-autosize": "^8.5.9", "server-only": "^0.0.1", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^6.10.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}