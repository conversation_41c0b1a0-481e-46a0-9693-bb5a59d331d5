# This is a config for E2B sandbox template.
# You can use template ID (ppnq7s6sftgs3a69a33o) or template name (vibe-nextjs-ajay-test) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("vibe-nextjs-ajay-test") # Sync sandbox
# sandbox = await AsyncSandbox.create("vibe-nextjs-ajay-test") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('vibe-nextjs-ajay-test')

team_id = "163ba2ad-32c8-477b-8954-141a8c2ce8d4"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "vibe-nextjs-ajay-test"
template_id = "ppnq7s6sftgs3a69a33o"
